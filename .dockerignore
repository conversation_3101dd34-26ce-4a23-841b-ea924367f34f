# Include pattern: ignore everything first, then include what we need
*

# Include source code
!src/
!src/**

# Include package configuration
!package.json
!pnpm-lock.yaml

# Include TypeScript configuration
!tsconfig.json

# Include code formatting configuration
!.prettierrc*
!.prettierignore

# Include essential configuration files
!.env.example

# Always exclude these even if they match include patterns
node_modules/
.git/
.env
.env.*
!.env.example
*.log
.DS_Store
Thumbs.db
dist/
output/
.mastra/