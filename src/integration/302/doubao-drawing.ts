import { switchMap } from 'rxjs'
import { fromFetch } from 'rxjs/fetch'

import type { X302Context } from './config'

export type DoubaoModel = 'general_v3.0'

export type DoubaoDrawingInput = {
  /**
   * 用于生成图像的提示词，中英文均可
   * 提示：用引号将文字引起来可提高文字准确率
   */
  prompt: string
  /**
   * 模型版本
   * @default 'general_v3.0'
   */
  model_version?: DoubaoModel
  /**
   * 请求调度配置
   * @default 'general_v20_9B_pe'
   * - general_v20_9B_pe: 标准版
   * - general_v20_9B_rephraser: 美感版
   */
  req_schedule_conf?: 'general_v20_9B_pe' | 'general_v20_9B_rephraser'
  /**
   * LLM种子
   */
  llm_seed?: string
  /**
   * 随机种子，-1表示随机
   * @default -1
   */
  seed?: number
  /**
   * 影响文本描述的程度
   * @default 3.5
   * @min 1
   * @max 10
   */
  scale?: number
  /**
   * 生成图像的步数
   * @default 25
   * @min 1
   * @max 200
   * 推荐: 1-50
   */
  ddim_steps?: number
  /**
   * 生成图像的宽度
   * @default 512
   * @min 256
   * @max 768
   * 注意: width * height 必须大于 512*512
   */
  width?: number
  /**
   * 生成图像的高度
   * @default 512
   * @min 256
   * @max 768
   * 注意: width * height 必须大于 512*512
   */
  height?: number
  /**
   * 是否开启文本扩写优化
   * @default true
   * 短prompt建议开启，长prompt建议关闭
   */
  use_pre_llm?: boolean
  /**
   * 是否使用超分辨率
   * @default true
   * true: 文生图+AIGC超分
   * false: 仅文生图
   */
  use_sr?: boolean
  /**
   * 超分辨率种子
   */
  sr_seed?: number
  /**
   * 超分辨率强度
   */
  sr_strength?: number
  /**
   * 超分辨率尺度
   */
  sr_scale?: number
  /**
   * 超分辨率步数
   */
  sr_steps?: number
  /**
   * 是否仅进行超分辨率
   */
  is_only_sr?: boolean
  /**
   * 是否返回图片URL（有效期24小时）
   * @default false
   */
  return_url?: boolean
  /**
   * 水印配置
   */
  logo_info?: {
    /**
     * 是否添加水印
     */
    add_logo: boolean
    /**
     * 水印位置
     * @default 0
     * 0: 右下角, 1: 左下角, 2: 左上角, 3: 右上角
     */
    position?: number
    /**
     * 水印语言
     * @default 0
     * 0: 中文(AI生成), 1: 英文(Generated by AI)
     */
    language?: number
    /**
     * 水印不透明度
     * @default 0.3
     * @min 0
     * @max 1
     */
    opacity?: number
    /**
     * 自定义水印文字
     */
    logo_text_content?: string
  }
}

export type DoubaoDrawingOutput = {
  code: number
  message: string
  status: number
  request_id: string
  time_elapsed: string
  data: {
    algorithm_base_resp: {
      status_code: number
      status_message: string
    }
    binary_data_base64: string[]
    image_urls: string[]
    llm_result: string
    pe_result: string
    predict_tags_result: string
    rephraser_result: string
    request_id: string
    vlm_result: string
  }
}

const DOUBAO_ENDPOINT = 'https://api.302.ai/doubao/drawing'

export function generateDoubaoImage(
  input: DoubaoDrawingInput,
  ctx: X302Context,
) {
  const requestBody = {
    ...input,
    model_version: input.model_version || 'general_v3.0',
  }

  return fromFetch(DOUBAO_ENDPOINT, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${ctx.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  }).pipe(
    switchMap(async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = (await response.json()) as DoubaoDrawingOutput
      if (data.code !== 10000) {
        throw new Error(`Doubao API Error: ${data.message}`)
      }
      if (data.data.algorithm_base_resp.status_code !== 0) {
        throw new Error(
          `Doubao Algorithm Error: ${data.data.algorithm_base_resp.status_message}`,
        )
      }
      return data
    }),
  )
}
