## Wavespeed

- Introduction: https://wavespeed.ai/docs
- Webhook: https://wavespeed.ai/docs-api/webhooks
- Media Upload: https://wavespeed.ai/docs-common-api/media-upload
- Flux Kontext Pro: https://wavespeed.ai/docs-api/flux-kontext-pro
- Flux Kontext Pro Multi: https://wavespeed.ai/docs-api/flux-kontext-pro-multi
- Flux Kontext Pro Text to Image: https://wavespeed.ai/docs-api/flux-kontext-pro-text-to-image
- Kling v2.1 I2V Master: https://wavespeed.ai/docs-api/kling-v2.1-i2v-master
- Bytedance Seedance v1 Lite I2V 480p: https://wavespeed.ai/docs-api/bytedance-seedance-v1-lite-i2v-480p

## 302

- GPT-Image:
  - Image generate: https://302ai.apifox.cn/api-288853804
  - Image edit: https://302ai.apifox.cn/api-288853817
- Midjourney Image:
  - Imagine: https://302ai.apifox.cn/api-160578879
  - Get taks status: https://302ai.apifox.cn/api-160579461
  - Cancel task: https://302ai.apifox.cn/api-190907177

- Midjourney Video
  - Generate: https://302ai.apifox.cn/api-315639885
  - Extension: https://302ai.apifox.cn/api-315673619
  - Get task status: https://302ai.apifox.cn/api-315642081
- Recraft Image:
  - Recraft V3 image: https://302ai.apifox.cn/api-228942858

- Google Imagine v4: https://302ai.apifox.cn/api-298871178

## Huiyan

- Introduction: https://huiyan.ai/docs

## Minimax

- Text to audio: https://www.minimax.io/platform/document/T2A%20V2?key=66719005a427f0c8a5701643#TJeyxusWAUP0l3tX67brbAyE
- Voice Cloning: https://www.minimax.io/platform/document/Voice%20Cloning?key=66719032a427f0c8a570165b#GuPbbFheke90twNM9xfS1Akp
- Video generation: https://www.minimax.io/platform/document/video_generation?key=66d1439376e52fcee2853049#HiFaU52pEy74OrHtdn4Po4ub
- Image generation: https://www.minimax.io/platform/document/wUkQTKNUuC8mJttAvXqCxG3D?key=67b7148bb74bdd7459f7b6ad
