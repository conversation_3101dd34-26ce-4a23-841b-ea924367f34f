# Convex 集成项目 - 问答记录

> 本文档记录在编写 Convex 集成项目计划过程中产生的所有问题，用户会在这里用 `>` 回答。

## 技术问题

### 阶段一：VFS 迁移相关问题

- [ ] Convex File Storage vs R2 组件的选择策略？
- [ ] 大文件存储的成本对比（Convex File Storage vs R2）？
- [ ] VFS 路径索引设计（支持目录遍历和模糊匹配）？
- [ ] 并发读写的乐观锁实现？
- [ ] 开发环境 vs 生产环境的数据隔离策略？
- [ ] 现有 S3 数据迁移到 Convex 生态的策略？
- [ ] 文件版本控制和历史记录需求？

### 阶段二：事件总线相关问题

- [ ] 是否需要支持任务的暂停/恢复功能？
- [ ] 同一用户的资产生成任务是否需要排队机制？

### 阶段三：Mastra Storage 相关问题

- [ ] MastraStorage 接口的完整实现要求？
- [ ] 需要迁移的数据类型：threads, messages, resources, workflows, eval_datasets, traces
- [ ] 消息格式 V1/V2 的兼容性处理？
- [ ] BigInt（traces 的时间戳）在 Convex 中的处理方式？

## 业务问题

### 产品需求确认

- ✅ **事件范围**：长任务中间结果实时写入，支持完整的对话和生成过程回放
- ✅ **事件消费**：前端 UI 实时更新
- ✅ **数据保留**：事件永久存储在 Convex 表中
- ✅ **迁移策略**：渐进式迁移（VFS → 事件 → Mastra 存储）
- [ ] **回滚计划**：如果 Convex 集成出现问题的应急方案？

> 不能, 也不想, pg 没有 realtime, 我需要用不同的系统解决问题, 架构变的太复杂

## 架构问题

### 核心决策

- ✅ **存储选择**：二选一策略，选择 R2 组件（成本更低）
- ✅ **并发控制**：依赖 Convex 原生事务性，VFS 读写对应表行操作
- ✅ **实时方案**：移除 PartySocket，使用 Convex 实时订阅
- ✅ **查询复杂度**：无复杂查询需求，性能无忧
- ✅ **数据迁移**：当前 R2（S3 接口），未上线，无迁移成本
- ✅ **混合架构**：不保留 PostgreSQL，避免多系统复杂性

### 待确认架构问题

- [ ] **环境隔离**：dev/staging/prod 环境的 Convex 项目隔离策略？
- [ ] **故障处理**：Convex 服务不可用时的降级方案？

> BaaS 都挂了, 那我这小小的产品只能等啊..

- [ ] **监控集成**：如何将 Convex 的监控数据集成到现有的观测体系？

> 监控有两层, convex 这边是个 BaaS 只能选择信任, mastra 应用层有自有的遥测体系

- ✅ **成本控制**：不需要特别考虑

### 存储策略问题

- [ ] **存储策略优化**：

> 没必要啊, 我们只有图像, 音频, 视频, 视频长度也很难超 180s, 直接存 R2 就行了, convex 的 r2 组件本质上就是一堆 convex table + api 封装出来的文件 CRUD 接口, 我们用 convex-r2 解决存储问题就行了

## 风险评估问题

### 中风险

- [ ] 开发环境与生产环境配置差异
- [ ] 第三方依赖的版本兼容性
- [ ] 事件处理的延迟问题

> 这里的事件其实是 convex table raw 的读写. 延迟问题取决于网络架构, 比如服务器和 convex 物理距离太远. 存储本身没什么影响

### 低风险

- [ ] 学习曲线和开发效率影响

> 有计划就不怕, 在做之前会收集所有问题的解决方案

- [ ] 监控和调试工具的适配

## 实施相关问题

### 数据迁移问题

- ✅ **数据迁移**：当前使用 R2（S3 接口），未上线，无迁移成本

### 性能问题

- [ ] 查询性能提升预期？
- [ ] 并发处理能力评估？

### 兼容性问题

- [ ] 向后兼容性保持策略？
- [ ] API 接口变更影响评估？

---

**使用说明**：

- 已确认的问题标记为 ✅
- 待确认的问题标记为 [ ]
- 用户回答使用 `>` 引用格式
- 新问题请按类别添加到相应章节
